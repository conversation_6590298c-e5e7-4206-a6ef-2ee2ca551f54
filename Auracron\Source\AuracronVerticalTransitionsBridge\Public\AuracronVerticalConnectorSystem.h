/**
 * AuracronVerticalConnectorSystem.h
 * 
 * Sistema completo de conectores verticais para transições entre camadas
 * Implementa os 5 tipos específicos de conectores mencionados no design document:
 * 1. Portais de Ânima - permanentes nas bases
 * 2. Fendas Fluxo - temporárias, rasgam o solo
 * 3. Cipós Astria - cordas vegetais escaláveis
 * 4. Elevadores de Vórtice - colunas de vento
 * 5. Respiradouros Geotermais - ativação periódica
 * 
 * Usa UE 5.6 APIs modernas para transições suaves e otimização de performance.
 */

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Components/CapsuleComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/TimelineComponent.h"
#include "Curves/CurveFloat.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "TimerManager.h"
#include "AuracronVerticalConnectorSystem.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class AAuracronVerticalConnector;

/**
 * Tipos de conectores verticais
 */
UENUM(BlueprintType)
enum class EVerticalConnectorType : uint8
{
    None                UMETA(DisplayName = "None"),
    PortalAnima         UMETA(DisplayName = "Portal de Ânima"),
    FendaFluxo          UMETA(DisplayName = "Fenda Fluxo"),
    CipoAstria          UMETA(DisplayName = "Cipó Astria"),
    ElevadorVortice     UMETA(DisplayName = "Elevador de Vórtice"),
    RespiradoroGeotermal UMETA(DisplayName = "Respiradouro Geotermal")
};

/**
 * Estados do conector vertical
 */
UENUM(BlueprintType)
enum class EVerticalConnectorState : uint8
{
    Inactive            UMETA(DisplayName = "Inactive"),
    Activating          UMETA(DisplayName = "Activating"),
    Active              UMETA(DisplayName = "Active"),
    InUse               UMETA(DisplayName = "In Use"),
    Deactivating        UMETA(DisplayName = "Deactivating"),
    Cooldown            UMETA(DisplayName = "Cooldown"),
    Broken              UMETA(DisplayName = "Broken")
};

/**
 * Camadas de realm para transição
 */
UENUM(BlueprintType)
enum class ERealmLayer : uint8
{
    None                UMETA(DisplayName = "None"),
    Terrestrial         UMETA(DisplayName = "Planície Radiante"),
    Celestial           UMETA(DisplayName = "Firmamento Zephyr"),
    Abyssal             UMETA(DisplayName = "Abismo Umbrio")
};

/**
 * Configuração de conector vertical
 */
USTRUCT(BlueprintType)
struct AURACRONVERTICALTRANSITIONSBRIDGE_API FVerticalConnectorConfig
{
    GENERATED_BODY()

    /** Tipo do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    EVerticalConnectorType ConnectorType;

    /** Camada de origem */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    ERealmLayer SourceLayer;

    /** Camada de destino */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    ERealmLayer DestinationLayer;

    /** Localização do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    FVector ConnectorLocation;

    /** Raio de ativação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    float ActivationRadius;

    /** Velocidade de transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    float TransitionSpeed;

    /** Duração da transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    float TransitionDuration;

    /** Cooldown após uso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    float CooldownDuration;

    /** Custo de energia para usar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    float EnergyCost;

    /** Máximo de usuários simultâneos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    int32 MaxSimultaneousUsers;

    /** Requer linha de visão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    bool bRequiresLineOfSight;

    /** Pode ser usado em combate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    bool bUsableInCombat;

    /** Ativação automática */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    bool bAutoActivation;

    /** Intervalo de ativação automática (para Respiradouros) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    float AutoActivationInterval;

    /** Duração da ativação automática */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connector Config")
    float AutoActivationDuration;

    FVerticalConnectorConfig()
    {
        ConnectorType = EVerticalConnectorType::None;
        SourceLayer = ERealmLayer::None;
        DestinationLayer = ERealmLayer::None;
        ConnectorLocation = FVector::ZeroVector;
        ActivationRadius = 500.0f;
        TransitionSpeed = 1000.0f;
        TransitionDuration = 2.0f;
        CooldownDuration = 5.0f;
        EnergyCost = 20.0f;
        MaxSimultaneousUsers = 1;
        bRequiresLineOfSight = false;
        bUsableInCombat = true;
        bAutoActivation = false;
        AutoActivationInterval = 60.0f;
        AutoActivationDuration = 15.0f;
    }
};

/**
 * Dados de transição vertical
 */
USTRUCT(BlueprintType)
struct AURACRONVERTICALTRANSITIONSBRIDGE_API FVerticalTransitionData
{
    GENERATED_BODY()

    /** Personagem em transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    TObjectPtr<ACharacter> TransitioningCharacter;

    /** Conector sendo usado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    TObjectPtr<AAuracronVerticalConnector> Connector;

    /** Posição inicial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    FVector StartPosition;

    /** Posição final */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    FVector EndPosition;

    /** Progresso da transição (0.0 - 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    float TransitionProgress;

    /** Tempo de início da transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    float StartTime;

    /** Duração total da transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    float Duration;

    /** Curva de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    TObjectPtr<UCurveFloat> MovementCurve;

    FVerticalTransitionData()
    {
        TransitioningCharacter = nullptr;
        Connector = nullptr;
        StartPosition = FVector::ZeroVector;
        EndPosition = FVector::ZeroVector;
        TransitionProgress = 0.0f;
        StartTime = 0.0f;
        Duration = 2.0f;
        MovementCurve = nullptr;
    }
};

/**
 * Sistema de conectores verticais
 * 
 * Gerencia todos os tipos de conectores verticais para transições entre camadas
 */
UCLASS(BlueprintType)
class AURACRONVERTICALTRANSITIONSBRIDGE_API UAuracronVerticalConnectorSystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    virtual void Tick(float DeltaTime) override;
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

    // === Core System Management ===
    
    /** Inicializar sistema de conectores */
    UFUNCTION(BlueprintCallable, Category = "Vertical Connectors")
    void InitializeConnectorSystem();

    /** Atualizar sistema de conectores */
    UFUNCTION(BlueprintCallable, Category = "Vertical Connectors")
    void UpdateConnectorSystem(float DeltaTime);

    /** Finalizar sistema de conectores */
    UFUNCTION(BlueprintCallable, Category = "Vertical Connectors")
    void ShutdownConnectorSystem();

    // === Connector Management ===
    
    /** Criar conector vertical */
    UFUNCTION(BlueprintCallable, Category = "Vertical Connectors")
    AAuracronVerticalConnector* CreateVerticalConnector(const FVerticalConnectorConfig& Config);

    /** Remover conector vertical */
    UFUNCTION(BlueprintCallable, Category = "Vertical Connectors")
    bool RemoveVerticalConnector(AAuracronVerticalConnector* Connector);

    /** Obter conectores por tipo */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Vertical Connectors")
    TArray<AAuracronVerticalConnector*> GetConnectorsByType(EVerticalConnectorType ConnectorType) const;

    /** Obter conectores por camada */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Vertical Connectors")
    TArray<AAuracronVerticalConnector*> GetConnectorsByLayer(ERealmLayer Layer) const;

    /** Obter conector mais próximo */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Vertical Connectors")
    AAuracronVerticalConnector* GetNearestConnector(const FVector& Location, EVerticalConnectorType ConnectorType = EVerticalConnectorType::None) const;

    // === Transition Management ===
    
    /** Iniciar transição vertical */
    UFUNCTION(BlueprintCallable, Category = "Vertical Connectors")
    bool StartVerticalTransition(ACharacter* Character, AAuracronVerticalConnector* Connector);

    /** Cancelar transição vertical */
    UFUNCTION(BlueprintCallable, Category = "Vertical Connectors")
    bool CancelVerticalTransition(ACharacter* Character);

    /** Obter progresso da transição */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Vertical Connectors")
    float GetTransitionProgress(ACharacter* Character) const;

    /** Verificar se personagem está em transição */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Vertical Connectors")
    bool IsCharacterInTransition(ACharacter* Character) const;

    // === Events ===
    
    /** Evento quando transição inicia */
    UFUNCTION(BlueprintImplementableEvent, Category = "Vertical Connectors")
    void OnTransitionStarted(ACharacter* Character, AAuracronVerticalConnector* Connector);

    /** Evento quando transição completa */
    UFUNCTION(BlueprintImplementableEvent, Category = "Vertical Connectors")
    void OnTransitionCompleted(ACharacter* Character, AAuracronVerticalConnector* Connector);

    /** Evento quando transição é cancelada */
    UFUNCTION(BlueprintImplementableEvent, Category = "Vertical Connectors")
    void OnTransitionCancelled(ACharacter* Character, AAuracronVerticalConnector* Connector);

    /** Evento quando conector é ativado */
    UFUNCTION(BlueprintImplementableEvent, Category = "Vertical Connectors")
    void OnConnectorActivated(AAuracronVerticalConnector* Connector);

    /** Evento quando conector é desativado */
    UFUNCTION(BlueprintImplementableEvent, Category = "Vertical Connectors")
    void OnConnectorDeactivated(AAuracronVerticalConnector* Connector);

protected:
    // === Configuration ===
    
    /** Configurações padrão por tipo de conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<EVerticalConnectorType, FVerticalConnectorConfig> DefaultConfigurations;

    /** Curvas de movimento por tipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<EVerticalConnectorType, TObjectPtr<UCurveFloat>> MovementCurves;

    // === System State ===
    
    /** Conectores ativos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    TArray<TObjectPtr<AAuracronVerticalConnector>> ActiveConnectors;

    /** Transições ativas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    TMap<TObjectPtr<ACharacter>, FVerticalTransitionData> ActiveTransitions;

    /** Sistema inicializado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    bool bSystemInitialized;

private:
    // === Internal Implementation ===
    void InitializeDefaultConfigurations();
    void LoadMovementCurves();
    void ProcessActiveTransitions(float DeltaTime);
    void UpdateConnectorStates(float DeltaTime);
    void HandleAutoActivations(float DeltaTime);
    
    // === Transition Implementation ===
    void ProcessTransition(ACharacter* Character, FVerticalTransitionData& TransitionData, float DeltaTime);
    void CompleteTransition(ACharacter* Character, const FVerticalTransitionData& TransitionData);
    FVector CalculateTransitionPosition(const FVerticalTransitionData& TransitionData, float Progress);
    
    // === Utility Methods ===
    bool ValidateTransitionRequest(ACharacter* Character, AAuracronVerticalConnector* Connector);
    FVector GetDestinationPosition(AAuracronVerticalConnector* Connector, ERealmLayer DestinationLayer);
    void ApplyTransitionEffects(ACharacter* Character, AAuracronVerticalConnector* Connector, bool bStarting);
    
    // === Cached References ===
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedRealmSubsystem;
    
    // === Timers ===
    FTimerHandle SystemUpdateTimer;
    FTimerHandle ConnectorUpdateTimer;
    FTimerHandle AutoActivationTimer;
    
    // === Performance Tracking ===
    float LastUpdateTime;
    int32 ActiveTransitionCount;
    int32 TotalTransitionsProcessed;
};
